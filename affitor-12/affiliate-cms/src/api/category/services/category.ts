/**
 * category service.
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::category.category', ({ strapi }) => ({
  // Get categories overview with metrics
  async getCategoriesOverview({ search, sort }) {
    try {
      // Build filters for search
      const filters: any = {};
      if (search) {
        filters.$or = [
          { name: { $containsi: search } },
          { description: { $containsi: search } }
        ];
      }

      // Get all categories with their affiliates
      const categories = await strapi.documents('api::category.category').findMany({
        filters,
        populate: {
          affiliates: {
            populate: {
              commission: true,
              social_logs: true,
              traffic_webs: true,
            }
          }
        }
      });

      // Calculate metrics for each category
      const categoriesWithMetrics = await Promise.all(
        categories.map(async (category) => {
          const metrics = await this.calculateCategoryMetrics(category);
          return {
            ...category,
            ...metrics
          };
        })
      );

      // Apply sorting
      if (sort) {
        categoriesWithMetrics.sort((a, b) => {
          const [field, order] = sort.split(':');
          const aValue = a[field] || 0;
          const bValue = b[field] || 0;

          if (order === 'desc') {
            return bValue - aValue;
          }
          return aValue - bValue;
        });
      }

      return {
        data: categoriesWithMetrics,
        meta: {
          total: categoriesWithMetrics.length
        }
      };
    } catch (error) {
      console.error('Error in getCategoriesOverview service:', error);
      throw error;
    }
  },

  // Calculate metrics for a category
  async calculateCategoryMetrics(category) {
    const affiliates = category.affiliates || [];

    if (affiliates.length === 0) {
      return {
        avgEPU: 0,
        topPrograms: [],
        topVideos: [],
        totalPrograms: 0,
        totalVideos: 0,
        totalViews: 0,
        avgMonthlyTraffic: 0
      };
    }

    // Calculate average EPU (using commission data)
    const validCommissions = affiliates
      .filter(affiliate => affiliate.commission?.avg_commission)
      .map(affiliate => affiliate.commission.avg_commission);

    const avgEPU = validCommissions.length > 0
      ? validCommissions.reduce((sum, commission) => sum + commission, 0) / validCommissions.length
      : 0;

    // Get top 3 programs by monthly traffic
    const topPrograms = affiliates
      .filter(affiliate => affiliate.monthly_traffic)
      .sort((a, b) => (b.monthly_traffic || 0) - (a.monthly_traffic || 0))
      .slice(0, 3)
      .map(affiliate => ({
        id: affiliate.documentId,
        name: affiliate.name,
        image: affiliate.image,
        epu: affiliate.commission?.avg_commission || 0,
        commission: affiliate.commission?.title || 'N/A',
        monthlyTraffic: affiliate.monthly_traffic || 0
      }));

    // Get social content data for top videos
    const affiliateIds = affiliates.map(affiliate => affiliate.documentId);
    const socialContent = await strapi.documents('api::social-listening.social-listening').findMany({
      filters: {
        affiliate: { documentId: { $in: affiliateIds } },
        type: 'video'
      },
      sort: { views: 'desc' },
      limit: 3,
      populate: {
        affiliate: true
      }
    });

    const topVideos = socialContent.map(video => ({
      id: video.documentId,
      title: video.title,
      thumbnail: video.thumbnail,
      views: video.views || 0,
      platform: video.platform,
      link: video.link,
      affiliate: video.affiliate?.name
    }));

    // Calculate total videos and views
    const allSocialContent = await strapi.documents('api::social-listening.social-listening').findMany({
      filters: {
        affiliate: { documentId: { $in: affiliateIds } }
      }
    });

    const totalVideos = allSocialContent.length;
    const totalViews = allSocialContent.reduce((sum, content) => sum + (content.views || 0), 0);

    // Calculate average monthly traffic
    const validTraffic = affiliates
      .filter(affiliate => affiliate.monthly_traffic)
      .map(affiliate => affiliate.monthly_traffic);

    const avgMonthlyTraffic = validTraffic.length > 0
      ? validTraffic.reduce((sum, traffic) => sum + traffic, 0) / validTraffic.length
      : 0;

    return {
      avgEPU: Math.round(avgEPU * 100) / 100,
      topPrograms,
      topVideos,
      totalPrograms: affiliates.length,
      totalVideos,
      totalViews,
      avgMonthlyTraffic: Math.round(avgMonthlyTraffic)
    };
  },

  // Get category detail
  async getCategoryDetail(slug) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug },
        populate: {
          affiliates: {
            populate: {
              commission: true,
              social_logs: true,
            }
          }
        }
      });

      if (!category) {
        return null;
      }

      const metrics = await this.calculateCategoryMetrics(category);

      return {
        ...category,
        ...metrics
      };
    } catch (error) {
      console.error('Error in getCategoryDetail service:', error);
      throw error;
    }
  },

  // Get category programs with filtering and sorting
  async getCategoryPrograms(slug, { search, sort, pagination = { page: 1, pageSize: 10 } }) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug }
      });

      if (!category) {
        throw new Error('Category not found');
      }

      // Build filters
      const filters: any = {
        categories: { documentId: category.documentId }
      };

      if (search) {
        filters.$or = [
          { name: { $containsi: search } },
          { company_name: { $containsi: search } }
        ];
      }

      // Build sort
      const sortOptions: any = {};
      if (sort) {
        const [field, order] = sort.split(':');
        sortOptions[field] = order;
      } else {
        sortOptions.name = 'asc'; // default sort by name
      }

      const affiliates = await strapi.documents('api::affiliate.affiliate').findMany({
        filters,
        sort: sortOptions,
        pagination,
        populate: {
          commission: true,
          image: true,
          categories: true,
          social_logs: true
        }
      });

      // Get social content counts for each affiliate
      const affiliatesWithMetrics = await Promise.all(
        affiliates.data.map(async (affiliate) => {
          const socialContent = await strapi.documents('api::social-listening.social-listening').findMany({
            filters: {
              affiliate: { documentId: affiliate.documentId }
            }
          });

          const totalVideos = socialContent.length;
          const totalViews = socialContent.reduce((sum, content) => sum + (content.views || 0), 0);

          return {
            ...affiliate,
            totalVideos,
            totalViews
          };
        })
      );

      return {
        data: affiliatesWithMetrics,
        meta: affiliates.meta
      };
    } catch (error) {
      console.error('Error in getCategoryPrograms service:', error);
      throw error;
    }
  },

  // Get category social content
  async getCategorySocialContent(slug, { platforms, sort, pagination = { page: 1, pageSize: 10 } }) {
    try {
      const category = await strapi.documents('api::category.category').findFirst({
        filters: { slug },
        populate: {
          affiliates: true
        }
      });

      if (!category) {
        throw new Error('Category not found');
      }

      const affiliateIds = category.affiliates.map(affiliate => affiliate.documentId);

      // Build filters
      const filters: any = {
        affiliate: { documentId: { $in: affiliateIds } }
      };

      if (platforms && platforms.length > 0) {
        filters.platform = { $in: platforms };
      }

      // Build sort
      const sortOptions: any = {};
      if (sort) {
        const [field, order] = sort.split(':');
        sortOptions[field] = order;
      } else {
        sortOptions.views = 'desc'; // default sort by views
      }

      const socialContent = await strapi.documents('api::social-listening.social-listening').findMany({
        filters,
        sort: sortOptions,
        pagination,
        populate: {
          affiliate: true
        }
      });

      return socialContent;
    } catch (error) {
      console.error('Error in getCategorySocialContent service:', error);
      throw error;
    }
  }
}));
