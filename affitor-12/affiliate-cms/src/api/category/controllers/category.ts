/**
 *  category controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::category.category', ({ strapi }) => ({
  // Get categories overview with metrics
  async getCategoriesOverview(ctx) {
    try {
      const { search, sort } = ctx.query;

      const result = await strapi
        .service('api::category.category')
        .getCategoriesOverview({ search, sort });

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategoriesOverview:', err);
      ctx.throw(500, 'Failed to fetch categories overview');
    }
  },

  // Get category detail with summary metrics
  async getCategoryDetail(ctx) {
    try {
      const { slug } = ctx.params;

      const result = await strapi
        .service('api::category.category')
        .getCategoryDetail(slug);

      if (!result) {
        ctx.throw(404, 'Category not found');
      }

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategoryDetail:', err);
      if (err.status === 404) {
        ctx.throw(404, err.message);
      }
      ctx.throw(500, 'Failed to fetch category detail');
    }
  },

  // Get category programs with filtering and sorting
  async getCategoryPrograms(ctx) {
    try {
      const { slug } = ctx.params;
      const { search, sort, pagination } = ctx.query;

      const result = await strapi
        .service('api::category.category')
        .getCategoryPrograms(slug, { search, sort, pagination });

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategoryPrograms:', err);
      ctx.throw(500, 'Failed to fetch category programs');
    }
  },

  // Get category social content
  async getCategorySocialContent(ctx) {
    try {
      const { slug } = ctx.params;
      const { platforms, sort, pagination } = ctx.query;

      const result = await strapi
        .service('api::category.category')
        .getCategorySocialContent(slug, { platforms, sort, pagination });

      ctx.body = result;
    } catch (err) {
      console.error('Error in getCategorySocialContent:', err);
      ctx.throw(500, 'Failed to fetch category social content');
    }
  },
}));
