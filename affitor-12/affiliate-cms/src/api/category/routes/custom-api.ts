export default {
  routes: [
    {
      method: 'GET',
      path: '/categories/overview',
      handler: 'category.getCategoriesOverview',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/detail',
      handler: 'category.getCategoryDetail',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/programs',
      handler: 'category.getCategoryPrograms',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/categories/:slug/social-content',
      handler: 'category.getCategorySocialContent',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
