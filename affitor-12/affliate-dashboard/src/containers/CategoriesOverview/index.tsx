import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { actions } from "@/features/categories-overview/categories-overview.slice";
import {
  selectCategoriesOverviewList,
  selectCategoriesOverviewLoading,
  selectCategoriesOverviewError,
  selectCategoriesOverviewSearch,
  selectCategoriesOverviewSort,
} from "@/features/categories-overview/categories-overview.slice";
import { CircularProgress } from "@mui/material";
import { SearchIcon, SortAscIcon, SortDescIcon } from "lucide-react";
import CategoryCard from "./CategoryCard";
import { ICategoryOverview } from "@/interfaces";
import { CustomButton } from "@/components/CustomButton";

const CategoriesOverview: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  
  const categories = useSelector(selectCategoriesOverviewList);
  const loading = useSelector(selectCategoriesOverviewLoading);
  const error = useSelector(selectCategoriesOverviewError);
  const searchTerm = useSelector(selectCategoriesOverviewSearch);
  const sortOption = useSelector(selectCategoriesOverviewSort);

  const [localSearch, setLocalSearch] = useState(searchTerm);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearch !== searchTerm) {
        dispatch(actions.fetchCategoriesOverview({ 
          search: localSearch,
          sort: sortOption || undefined
        }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localSearch, searchTerm, sortOption, dispatch]);

  // Initial fetch
  useEffect(() => {
    dispatch(actions.fetchCategoriesOverview({}));
  }, [dispatch]);

  const handleSortChange = useCallback((field: string) => {
    const newOrder = 
      sortOption?.field === field && sortOption?.order === "asc" 
        ? "desc" 
        : "asc";
    
    const newSort = { field, order: newOrder as "asc" | "desc" };
    
    dispatch(actions.fetchCategoriesOverview({ 
      search: searchTerm,
      sort: newSort
    }));
  }, [sortOption, searchTerm, dispatch]);

  const handleCategoryClick = useCallback((category: ICategoryOverview) => {
    router.push(`/categories/${category.slug}`);
  }, [router]);

  const getSortIcon = (field: string) => {
    if (sortOption?.field !== field) return null;
    return sortOption.order === "asc" ? <SortAscIcon size={16} /> : <SortDescIcon size={16} />;
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="text-center py-8">
          <p className="text-red-500 text-lg mb-4">{error}</p>
          <CustomButton
            onClick={() => dispatch(actions.fetchCategoriesOverview({}))}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Try Again
          </CustomButton>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Affiliate Program Categories
        </h1>
        <p className="text-gray-600">
          Explore affiliate programs organized by categories with performance metrics and insights
        </p>
      </div>

      {/* Search and Sort Controls */}
      <div className="mb-6 flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search categories or programs..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Sort Options */}
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => handleSortChange("totalPrograms")}
            className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Programs {getSortIcon("totalPrograms")}
          </button>
          <button
            onClick={() => handleSortChange("totalVideos")}
            className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Videos {getSortIcon("totalVideos")}
          </button>
          <button
            onClick={() => handleSortChange("avgMonthlyTraffic")}
            className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Traffic {getSortIcon("avgMonthlyTraffic")}
          </button>
          <button
            onClick={() => handleSortChange("avgEPU")}
            className="flex items-center gap-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Avg EPU {getSortIcon("avgEPU")}
          </button>
        </div>
      </div>

      {/* Categories Grid */}
      {loading ? (
        <div className="text-center py-12">
          <CircularProgress color="inherit" />
          <p className="mt-4 text-gray-600">Loading categories...</p>
        </div>
      ) : categories && categories.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <CategoryCard
              key={category.documentId}
              category={category}
              onClick={() => handleCategoryClick(category)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">
            {searchTerm ? "No categories found matching your search" : "No categories available"}
          </p>
        </div>
      )}
    </div>
  );
};

export default CategoriesOverview;
