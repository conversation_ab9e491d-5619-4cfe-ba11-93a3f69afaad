import React from "react";
import { GetServerSideProps } from "next";
import <PERSON> from "next/head";
import CategoriesOverview from "@/containers/CategoriesOverview";

const CategoriesOverviewPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Affiliate Program Categories - Affitor</title>
        <meta 
          name="description" 
          content="Explore affiliate programs organized by categories with performance metrics, top programs, and social content insights." 
        />
        <meta name="keywords" content="affiliate programs, categories, marketing, performance metrics" />
      </Head>
      
      <CategoriesOverview />
    </>
  );
};

export default CategoriesOverviewPage;

// Optional: Add server-side props if needed for SEO or initial data
export const getServerSideProps: GetServerSideProps = async () => {
  return {
    props: {},
  };
};
